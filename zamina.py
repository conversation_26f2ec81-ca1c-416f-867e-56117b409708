import os
from docx import Document
from docx.enum.text import WD_ALIGN_PARAGRAPH

# 🔷 Шлях до папки з Word-файлами та файлу dyploma.docx
folder_path = r"C:\Users\<USER>\Desktop\ЗВ-41"
dyploma_path = r"C:\Users\<USER>\Desktop\dyploma.docx"

#  Список замін (очищений від помилкових записів)
replacements = {
    'Назва кваліфікації та присвоєний ступінь': 'Назва освітньої кваліфікації та присвоєний освітньо-професійний ступінь (мовою оригіналу)',
    'Name of qualification and (if applicable) title conferred': 'Name of educational qualification and educational-professional degree conferred (in original language)',
    'Ступінь вищої освіти': 'Освітньо-професійний ступінь фахової передвищої освіти',
    'Degree': 'Professional pre-higher education educational-professional degree',
    'Професійна кваліфікація (у разі присвоєння)': 'Освітньо-професійна програма',
    'Professional Qualification (if awarded)': 'Educational-professional programme',
    'Фахівець з геодезії та землеустрою': '',
    'Specialist in geodesy and land management': '',
    'Основна (основні) галузь (галузі) знань за кваліфікацією': 'Професійна кваліфікація (у разі присвоєння)',
    'Main field(s) of study for the qualification': 'Professional qualification (if awarded)',
    '19 Архітектура та будівництво': 'Фахівець з геодезії та землеустрою',
    '19 Architecture and Construction': 'Specialist in geodesy and land management',
    'Найменування і статус закладу (якщо відмінні від п. 2.3), який реалізує освітню програму': '',
    'Name and status of institution (if different from 2.3)': '',
    'administering studies': '',
    'Зазначено у пункті 2.3': '',
    'Specified in 2.3': '',
    '2.5': '2.4',
    'ІНФОРМАЦІЯ ПРО РІВЕНЬ КВАЛІФІКАЦІЇ І ТРИВАЛІСТЬ ЇЇ ЗДОБУТТЯ': 'ІНФОРМАЦІЯ ПРО КВАЛІФІКАЦІЮ І ТРИВАЛІСТЬ ЇЇ ЗДОБУТТЯ',
    'Тривалість освітньої програми в кредитах та/або роках': 'Офіційна тривалість освітньо-професійної програми в кредитах та/або роках',
    'Official duration of programme in credits and/or years': 'Official length of educational-professional programme in credits and/or years',
    'ІНФОРМАЦІЯ ПРО ЗАВЕРШЕНУ ОСВІТНЮ ПРОГРАМУ ТА ЗДОБУТІ РЕЗУЛЬТАТИ НАВЧАННЯ': 'ІНФОРМАЦІЯ ПРО ЗАВЕРШЕНУ ОСВІТНЬО-ПРОФЕСІЙНУ ПРОГРАМУ ТА ЗДОБУТІ РЕЗУЛЬТАТИ НАВЧАННЯ',
    'INFORMATION ON THE PROGRAMME COMPLETED AND THE RESULTS OBTAINED': 'INFORMATION ON THE COMPLETED EDUCATIONAL-PROFESSIONAL PROGRAMME AND LEARNING OUTCOMES',
    'Найменування всіх закладів вищої освіти (наукових установ) (відокремлених структурних підрозділів закладів вищої освіти), у яких здобувалася кваліфікація (у тому числі заклади освіти, в яких здобувач вищої освіти вивчав окремі дисципліни за програмами академічної мобільності)': 'Найменування всіх закладів фахової передвищої освіти (структурних підрозділів або філій закладів фахової передвищої освіти), у яких здобувалася освітня кваліфікація (у тому числі заклади освіти, в яких здобувач фахової передвищої освіти вивчав окремі дисципліни за програмами академічної мобільності)',
    'Name of all higher education (research) institutions (separate structural units of higher education institutions) where the qualification has been gained (including education institutions where the holder of the qualification has been studying separate course units within the framework(s) of academic mobility)': 'Names of all professional pre-higher education institutions (professional pre-higher education institutions separate structural units or branches) the qualification was gained in (including education institutions where the student of professional pre-higher education studied separate course units within the framework of academic mobility programme)',
    'Контактна інформація закладу вищої освіти (наукової установи)': 'Контактна інформація закладу фахової передвищої освіти (іншого суб’єкта освітньої діяльності)',
    'Contact information of the higher education (research) institution': 'Contact information of the professional pre-higher education institution (other educational entity)',
    'Керівник або уповноважена особа закладу вищої освіти': 'Керівник або уповноважена особа закладу фахової передвищої освіти',
    'Capacity': 'Head or other authorized person of professional pre-higher education institution ',
    'Посада керівника або іншої уповноваженої особи закладу вищої освіти (наукової установи)': 'Посада керівника або іншої уповноваженої особи закладу фахової передвищої освіти (іншого суб’єкта освітньої діяльності)',
    'Position of the Head or another authorized person of the Higher Education (Research) Institution': 'Position of the professional pre-higher education institution head or other authorized person (other educational entity) ',
    'Печатка': 'Офіційна печатка',
    'Official stamp or seal': 'Official Seal',
}

# Список ключів, для яких потрібно додати абзац і вирівнювання по ширині
keys_to_format = [
    'ІНФОРМАЦІЯ ПРО РІВЕНЬ КВАЛІФІКАЦІЇ І ТРИВАЛІСТЬ ЇЇ ЗДОБУТТЯ',
    'Тривалість освітньої програми в кредитах та/або роках',
    'Official duration of programme in credits and/or years',
    'ІНФОРМАЦІЯ ПРО ЗАВЕРШЕНУ ОСВІТНЮ ПРОГРАМУ ТА ЗДОБУТІ РЕЗУЛЬТАТИ НАВЧАННЯ',
    'INFORMATION ON THE PROGRAMME COMPLETED AND THE RESULTS OBTAINED',
    'Найменування всіх закладів вищої освіти (наукових установ) (відокремлених структурних підрозділів закладів вищої освіти), у яких здобувалася кваліфікація (у тому числі заклади освіти, в яких здобувач вищої освіти вивчав окремі дисципліни за програмами академічної мобільності) /Name of all higher education (research) institutions (separate structural units of higher education institutions) where the qualification has been gained (including education institutions where the holder of the qualification has been studying separate course units within the framework(s) of academic mobility)',
    'Контактна інформація закладу вищої освіти (наукової установи)',
    'Contact information of the higher education (research) institution',
    'Керівник або уповноважена особа закладу вищої освіти',
    'Capacity',
    'Посада керівника або іншої уповноваженої особи закладу вищої освіти (наукової установи)',
    'Position of the Head or another authorized person of the Higher Education (Research) Institution',
    'Печатка',
    'Official stamp or seal',
]

def get_dyploma_content(dyploma_path):
    """Читає таблицю з файлу dyploma.docx і повертає об'єкт таблиці."""
    if not os.path.exists(dyploma_path):
        print(f"❌ Файл {dyploma_path} не знайдено")
        return None

    try:
        print(f"📖 Читаємо файл: {dyploma_path}")
        doc = Document(dyploma_path)

        if doc.tables:
            print(f"� Знайдено {len(doc.tables)} таблиць")
            # Повертаємо першу таблицю
            table = doc.tables[0]
            print(f"✅ Таблиця має {len(table.rows)} рядків і {len(table.columns)} стовпців")
            return table
        else:
            print("❌ Таблиці не знайдено в dyploma.docx")
            return None

    except Exception as e:
        print(f"❌ Помилка при читанні файлу {dyploma_path}: {e}")
        return None

def replace_text_in_paragraph(paragraph, doc):
    """Обробляє параграф: застосовує заміни, видаляє за потреби, додає абзац і вирівнювання."""
    full_text = ''.join(run.text for run in paragraph.runs)
    changed = False
    needs_formatting = False

    # Перевірка на заміну або видалення
    for old, new in replacements.items():
        if old in full_text:
            if new == '':  # Якщо значення порожнє, видаляємо параграф
                paragraph._element.getparent().remove(paragraph._element)
                return False
            full_text = full_text.replace(old, new)
            changed = True
            if old in keys_to_format or new in keys_to_format:
                needs_formatting = True

    if changed:
        # Очищаємо всі run'и
        for run in paragraph.runs:
            run.text = ''

        if needs_formatting:
            # Розділяємо текст на рядки
            lines = [line.strip() for line in full_text.split('\n') if line.strip()]
            if len(lines) > 1:
                # Очищаємо оригінальний параграф
                paragraph.runs[0].text = lines[0]
                paragraph.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY  # Вирівнювання по ширині

                # Додаємо нові параграфи для решти рядків
                for line in lines[1:]:
                    new_paragraph = doc.add_paragraph(line)
                    new_paragraph.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
            else:
                # Якщо лише один рядок
                paragraph.runs[0].text = full_text
                paragraph.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
        else:
            # Якщо форматування не потрібне, просто оновлюємо текст
            paragraph.runs[0].text = full_text

    return True

def replace_text_in_cell(cell, doc):
    """Обробляє комірку, застосовуючи заміни та форматування."""
    for paragraph in cell.paragraphs:
        replace_text_in_paragraph(paragraph, doc)

def replace_pages_7_to_12(doc, dyploma_table):
    """Видаляє все після тексту 'ІНФОРМАЦІЯ ПРО НАЦІОНАЛЬНУ СИСТЕМУ ВИЩОЇ ОСВІТИ' і вставляє таблицю із dyploma.docx."""
    # Очищаємо колонтитули перед обробкою
    clear_headers_footers(doc)

    # Отримуємо всі параграфи документа
    paragraphs = list(doc.paragraphs)
    if not paragraphs:
        print("⚠️ Документ порожній")
        return

    # Шукаємо параграф з текстом "ІНФОРМАЦІЯ ПРО НАЦІОНАЛЬНУ СИСТЕМУ ВИЩОЇ ОСВІТИ"
    section_found = False
    section_index = -1

    for i, paragraph in enumerate(paragraphs):
        paragraph_text = ''.join(run.text for run in paragraph.runs).strip()
        # Шукаємо текст "ІНФОРМАЦІЯ ПРО НАЦІОНАЛЬНУ СИСТЕМУ ВИЩОЇ ОСВІТИ"
        if "ІНФОРМАЦІЯ ПРО НАЦІОНАЛЬНУ СИСТЕМУ ВИЩОЇ ОСВІТИ" in paragraph_text:
            section_found = True
            section_index = i
            print(f"✅ Знайдено розділ в параграфі {i}: {paragraph_text[:80]}...")
            break

    # Якщо не знайдено в параграфах, шукаємо в таблицях
    if not section_found:
        for table in doc.tables:
            for row_idx, row in enumerate(table.rows):
                for cell_idx, cell in enumerate(row.cells):
                    cell_text = cell.text.strip()
                    if "ІНФОРМАЦІЯ ПРО НАЦІОНАЛЬНУ СИСТЕМУ ВИЩОЇ ОСВІТИ" in cell_text:
                        # Знаходимо позицію таблиці і видаляємо все після неї
                        table_element = table._element
                        parent = table_element.getparent()
                        table_position = list(parent).index(table_element)

                        # Видаляємо все після цієї таблиці
                        elements_to_remove = list(parent)[table_position + 1:]
                        for element in elements_to_remove:
                            parent.remove(element)

                        section_found = True
                        print(f"✅ Знайдено розділ в таблиці: {cell_text[:80]}...")

                        # Додаємо таблицю із dyploma.docx після поточної таблиці
                        if dyploma_table:
                            print(f"📝 Додаємо таблицю з dyploma.docx")
                            copy_table_to_document(doc, dyploma_table)
                        return
                if section_found:
                    break
            if section_found:
                break

    if not section_found:
        print("⚠️ Текст 'ІНФОРМАЦІЯ ПРО НАЦІОНАЛЬНУ СИСТЕМУ ВИЩОЇ ОСВІТИ' не знайдено")
        return

    # Якщо знайдено в параграфах, видаляємо параграфи після нього
    if section_index >= 0:
        # Видаляємо всі параграфи після знайденого розділу (зберігаємо сам заголовок)
        paragraphs_to_remove = paragraphs[section_index + 1:]
        print(f"🗑️ Видаляємо {len(paragraphs_to_remove)} параграфів після розділу (зберігаємо заголовок)")

        for paragraph in reversed(paragraphs_to_remove):
            paragraph._element.getparent().remove(paragraph._element)

        # Додаємо таблицю із dyploma.docx
        if dyploma_table:
            print(f"📝 Додаємо таблицю з dyploma.docx")
            copy_table_to_document(doc, dyploma_table)

def clear_headers_footers(doc):
    """Очищає всі верхні та нижні колонтитули з документа."""
    try:
        for section in doc.sections:
            # Очищаємо верхні колонтитули
            if section.header:
                for paragraph in section.header.paragraphs:
                    paragraph.clear()

            # Очищаємо нижні колонтитули
            if section.footer:
                for paragraph in section.footer.paragraphs:
                    paragraph.clear()

            # Очищаємо колонтитули першої сторінки
            if hasattr(section, 'first_page_header') and section.first_page_header:
                for paragraph in section.first_page_header.paragraphs:
                    paragraph.clear()

            if hasattr(section, 'first_page_footer') and section.first_page_footer:
                for paragraph in section.first_page_footer.paragraphs:
                    paragraph.clear()

            # Очищаємо колонтитули парних сторінок
            if hasattr(section, 'even_page_header') and section.even_page_header:
                for paragraph in section.even_page_header.paragraphs:
                    paragraph.clear()

            if hasattr(section, 'even_page_footer') and section.even_page_footer:
                for paragraph in section.even_page_footer.paragraphs:
                    paragraph.clear()

        print("✅ Колонтитули очищено")
    except Exception as e:
        print(f"⚠️ Помилка при очищенні колонтитулів: {e}")


def copy_table_to_document(doc, source_table):
    """Копіює таблицю з dyploma.docx в поточний документ зі збереженням форматування."""
    from copy import deepcopy

    # Створюємо нову таблицю з такою ж структурою
    new_table = doc.add_table(rows=len(source_table.rows), cols=len(source_table.columns))

    # Копіюємо стиль таблиці
    try:
        if hasattr(source_table, 'style') and source_table.style:
            new_table.style = source_table.style
        else:
            new_table.style = 'Table Grid'
    except:
        pass

    # Копіюємо властивості таблиці
    try:
        if hasattr(source_table, '_tbl') and hasattr(new_table, '_tbl'):
            # Копіюємо ширину таблиці
            source_tbl_pr = source_table._tbl.tblPr
            target_tbl_pr = new_table._tbl.tblPr

            if source_tbl_pr is not None:
                # Копіюємо ширину таблиці
                tbl_w = source_tbl_pr.find('.//{http://schemas.openxmlformats.org/wordprocessingml/2006/main}tblW')
                if tbl_w is not None:
                    target_tbl_pr.append(deepcopy(tbl_w))
    except Exception as e:
        print(f"⚠️ Не вдалося скопіювати властивості таблиці: {e}")

    # Копіюємо вміст кожної комірки зі збереженням форматування
    for row_idx, source_row in enumerate(source_table.rows):
        target_row = new_table.rows[row_idx]

        # Копіюємо висоту рядка
        try:
            if hasattr(source_row, '_tr') and hasattr(target_row, '_tr'):
                source_tr_pr = source_row._tr.trPr
                if source_tr_pr is not None:
                    tr_height = source_tr_pr.find('.//{http://schemas.openxmlformats.org/wordprocessingml/2006/main}trHeight')
                    if tr_height is not None:
                        target_tr_pr = target_row._tr.get_or_add_trPr()
                        target_tr_pr.append(deepcopy(tr_height))
        except:
            pass

        for col_idx, source_cell in enumerate(source_row.cells):
            target_cell = target_row.cells[col_idx]

            # Очищаємо цільову комірку
            for paragraph in target_cell.paragraphs:
                paragraph.clear()

            # Копіюємо ширину комірки
            try:
                if hasattr(source_cell, '_tc') and hasattr(target_cell, '_tc'):
                    source_tc_pr = source_cell._tc.tcPr
                    if source_tc_pr is not None:
                        tc_w = source_tc_pr.find('.//{http://schemas.openxmlformats.org/wordprocessingml/2006/main}tcW')
                        if tc_w is not None:
                            target_tc_pr = target_cell._tc.get_or_add_tcPr()
                            target_tc_pr.append(deepcopy(tc_w))
            except:
                pass

            # Копіюємо параграфи зі збереженням форматування
            if source_cell.paragraphs:
                # Очищаємо всі існуючі параграфи в цільовій комірці
                target_cell._tc.clear_content()

                for para_idx, source_para in enumerate(source_cell.paragraphs):
                    # Додаємо новий параграф
                    target_para = target_cell.add_paragraph()

                    # Копіюємо вирівнювання параграфа
                    try:
                        target_para.alignment = source_para.alignment
                    except:
                        pass

                    # Копіюємо відступи та інтервали
                    try:
                        if source_para.paragraph_format.left_indent:
                            target_para.paragraph_format.left_indent = source_para.paragraph_format.left_indent
                        if source_para.paragraph_format.right_indent:
                            target_para.paragraph_format.right_indent = source_para.paragraph_format.right_indent
                        if source_para.paragraph_format.first_line_indent:
                            target_para.paragraph_format.first_line_indent = source_para.paragraph_format.first_line_indent
                        if source_para.paragraph_format.space_before:
                            target_para.paragraph_format.space_before = source_para.paragraph_format.space_before
                        if source_para.paragraph_format.space_after:
                            target_para.paragraph_format.space_after = source_para.paragraph_format.space_after
                    except:
                        pass

                    # Копіюємо run'и зі збереженням форматування
                    for source_run in source_para.runs:
                        target_run = target_para.add_run(source_run.text)

                        # Копіюємо форматування тексту
                        try:
                            if source_run.bold is not None:
                                target_run.bold = source_run.bold
                            if source_run.italic is not None:
                                target_run.italic = source_run.italic
                            if source_run.underline is not None:
                                target_run.underline = source_run.underline
                            if source_run.font.size:
                                target_run.font.size = source_run.font.size
                            if source_run.font.name:
                                target_run.font.name = source_run.font.name
                            if source_run.font.color.rgb:
                                target_run.font.color.rgb = source_run.font.color.rgb
                        except:
                            pass
            else:
                # Якщо немає параграфів у вихідній комірці, очищаємо цільову
                target_cell.text = ""

def replace_text_in_docx(file_path, dyploma_table):
    """Обробляє документ, замінюючи розділ після 'ІНФОРМАЦІЯ ПРО НАЦІОНАЛЬНУ СИСТЕМУ ВИЩОЇ ОСВІТИ', застосовуючи заміни та форматування."""
    try:
        doc = Document(file_path)

        # Замінюємо розділ після 'ІНФОРМАЦІЯ ПРО НАЦІОНАЛЬНУ СИСТЕМУ ВИЩОЇ ОСВІТИ'
        replace_pages_7_to_12(doc, dyploma_table)

        # Застосовуємо заміни та форматування до всього документа
        paragraphs_to_remove = []
        for paragraph in doc.paragraphs:
            if not replace_text_in_paragraph(paragraph, doc):
                paragraphs_to_remove.append(paragraph)

        # Обробка таблиць
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    replace_text_in_cell(cell, doc)

        doc.save(file_path)
        print(f'✅ Заміна сторінок 7–12, заміни та форматування виконано у файлі: {file_path}')
    except Exception as e:
        print(f'❌ Помилка при обробці файлу {file_path}: {e}')

# Отримуємо таблицю з файлу dyploma.docx
dyploma_table = get_dyploma_content(dyploma_path)

if dyploma_table:
    # Обробка всіх .docx файлів у папці
    for filename in os.listdir(folder_path):
        if filename.endswith('.docx') and not filename.startswith('~$'):
            full_path = os.path.join(folder_path, filename)
            replace_text_in_docx(full_path, dyploma_table)
else:
    print("❌ Не вдалося отримати таблицю з файлу dyploma.docx")
